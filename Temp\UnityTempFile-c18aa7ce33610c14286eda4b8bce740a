/target:library
/out:Temp/UnityEngine.TestRunner.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/UnityEngine.TestRunner.dll.ref
/deterministic
/debug:portable
/optimize-
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:D:/2024-5-15/UnityProject/SIPSorcerytest4.2/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AutoStreamingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CloudFoundationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIWidgetsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_FEATURES
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_48
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_IG
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:UNITY_UGP_API
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\AssemblyInfo.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\AllocatingGCMemoryConstraint.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\ConstraintsExtensions.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\InvalidSignatureException.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\Is.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\LogAssert.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\LogScope\ILogScope.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\LogScope\LogEvent.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\LogScope\LogMatch.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\LogScope\LogScope.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\OutOfOrderExpectedLogMessageException.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\UnexpectedLogMessageException.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\UnhandledLogMessageException.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Assertions\UnityTestTimeoutException.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\ActionDelegator.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Attributes\ConditionalIgnoreAttribute.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Attributes\TestEnumerator.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Attributes\TestMustExpectAllLogsAttribute.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Attributes\UnityCombinatorialStrategy.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Attributes\UnityPlatformAttribute.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Attributes\UnitySetUpAttribute.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Attributes\UnityTearDownAttribute.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Attributes\UnityTestAttribute.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\BaseDelegator.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\BeforeAfterTestCommandBase.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\BeforeAfterTestCommandState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\EnumerableApplyChangesToContextCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\EnumerableRepeatedTestCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\EnumerableRetryTestCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\EnumerableSetUpTearDownCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\EnumerableTestMethodCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\EnumerableTestState.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\ImmediateEnumerableCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\OuterUnityTestActionCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\SetUpTearDownCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\TestActionCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\TestCommandPcHelper.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Commands\UnityTestMethodCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\ConstructDelegator.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Filters\AssemblyNameFilter.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Filters\CategoryFilterExtended.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Filters\FullNameFilter.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\IAsyncTestAssemblyBuilder.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\IStateSerializer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\ITestSuiteModifier.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\OrderedTestSuiteModifier.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\CompositeWorkItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\CoroutineTestWorkItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\DefaultTestWorkItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\FailCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\IEnumerableTestMethodCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\PlaymodeWorkItemFactory.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\RestoreTestContextAfterDomainReload.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\TestCommandBuilder.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\UnityLogCheckDelegatingCommand.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\UnityTestAssemblyRunner.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\UnityTestExecutionContext.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\UnityWorkItem.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\UnityWorkItemDataHolder.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\Runner\WorkItemFactory.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\TestExtensions.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\TestResultExtensions.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\NUnitExtensions\UnityTestAssemblyBuilder.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\Callbacks\PlayModeRunnerCallback.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\Callbacks\PlayerQuitHandler.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\Callbacks\RemoteTestResultSender.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\Callbacks\TestResultRenderer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\Callbacks\TestResultRendererCallback.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\ITestRunnerListener.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\Messages\IEditModeTestYieldInstruction.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\PlaymodeTestsController.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\PlaymodeTestsControllerSettings.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\RemoteHelpers\IRemoteTestResultDataFactory.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\RemoteHelpers\PlayerConnectionMessageIds.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\RemoteHelpers\RemoteTestData.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\RemoteHelpers\RemoteTestResultData.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\RemoteHelpers\RemoteTestResultDataFactory.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\RemoteHelpers\RemoteTestResultDataWithTestData.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\RuntimeTestRunnerFilter.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\SynchronousFilter.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\TestEnumeratorWrapper.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\TestListenerWrapper.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\TestRunner\TestPlatform.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\AssemblyProvider\AssemblyLoadProxy.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\AssemblyProvider\AssemblyWrapper.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\AssemblyProvider\IAssemblyLoadProxy.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\AssemblyProvider\IAssemblyWrapper.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\AssemblyProvider\IScriptingRuntimeProxy.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\AssemblyProvider\PlayerTestAssemblyProvider.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\AssemblyProvider\ScriptingRuntimeProxy.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\AttributeHelper.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\ColorEqualityComparer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\CoroutineRunner.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\FloatEqualityComparer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\IOuterUnityTestAction.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\IPostBuildCleanup.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\IPrebuildSceneSetup.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\ITestRunCallback.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\MonoBehaviourTest\IMonoBehaviourTest.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\MonoBehaviourTest\MonoBehaviourTest.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\PostBuildCleanupAttribute.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\PrebuildSceneSetupAttribute.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\QuaternionEqualityComparer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\StacktraceFilter.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\TestRunCallbackAttribute.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\TestRunCallbackListener.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\Utils.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\Vector2ComparerWithEqualsOperator.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\Vector2EqualityComparer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\Vector3ComparerWithEqualsOperator.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\Vector3EqualityComparer.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\Vector4ComparerWithEqualsOperator.cs
D:\2024-5-15\UnityProject\SIPSorcerytest4.2\Library\PackageCache\com.unity.test-framework@1.1.33\UnityEngine.TestRunner\Utils\Vector4EqualityComparer.cs
