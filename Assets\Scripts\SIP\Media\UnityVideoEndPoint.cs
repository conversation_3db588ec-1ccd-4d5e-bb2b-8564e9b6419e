using SIPSorcery.Media;
using SIPSorcery.Net;
using SIPSorceryMedia.Abstractions;
using UnityEngine;
using UnityEngine.UI;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

/// <summary>
/// Unity视频端点实现，基于SIPSorceryMedia.FFmpeg
/// 实现IVideoSource和IVideoSink接口，提供视频捕获和渲染功能
/// </summary>
public class UnityVideoEndPoint : MonoBehaviour, IVideoSource, IVideoSink
{
    [Header("Video Settings")]
    [SerializeField] private int videoWidth = 640;
    [SerializeField] private int videoHeight = 480;
    [SerializeField] private int frameRate = 30;
    
    [Header("UI Components")]
    [SerializeField] private RawImage localVideoDisplay;
    [SerializeField] private RawImage remoteVideoDisplay;
    
    // Unity视频组件
    private WebCamTexture _webCamTexture;
    private Texture2D _remoteVideoTexture;
    
    // 视频格式和状态
    private VideoFormat _currentVideoFormat;
    private bool _isCapturing = false;
    private bool _isInitialized = false;
    
    // 视频缓冲区
    private readonly Queue<byte[]> _videoBuffer = new Queue<byte[]>();
    private readonly object _bufferLock = new object();

    // 视频编解码器（暂时使用简单实现，后续集成FFmpeg）
    private bool _encoderInitialized = false;
    
    #region IVideoSource Implementation

    public event EncodedSampleDelegate OnVideoSourceEncodedSample;
    public event RawVideoSampleDelegate OnVideoSourceRawSample;
    public event RawVideoSampleFasterDelegate OnVideoSourceRawSampleFaster;
    public event SourceErrorDelegate OnVideoSourceError;
    
    public List<VideoFormat> GetVideoSourceFormats()
    {
        return new List<VideoFormat>
        {
            new VideoFormat(VideoCodecsEnum.H264, 96, videoWidth, videoHeight, frameRate),
            new VideoFormat(VideoCodecsEnum.VP8, 97, videoWidth, videoHeight, frameRate)
        };
    }
    
    public void SetVideoSourceFormat(VideoFormat videoFormat)
    {
        _currentVideoFormat = videoFormat;
        Debug.Log($"Setting video source format: {videoFormat.Codec} {videoFormat.Width}x{videoFormat.Height}@{videoFormat.FrameRate}fps");
    }
    
    public Task StartVideo()
    {
        return Task.Run(() =>
        {
            try
            {
                StartWebCam();
                _isCapturing = true;
                Debug.Log("Video capture started");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to start video: {ex.Message}");
                throw;
            }
        });
    }
    
    public Task PauseVideo()
    {
        _isCapturing = false;
        return Task.CompletedTask;
    }
    
    public Task ResumeVideo()
    {
        _isCapturing = true;
        return Task.CompletedTask;
    }
    
    public Task CloseVideo()
    {
        StopWebCam();
        _isCapturing = false;
        return Task.CompletedTask;
    }

    public void RestrictFormats(Func<VideoFormat, bool> filter)
    {
        // 实现视频格式限制功能
        // 这里可以根据filter函数过滤支持的视频格式
        Debug.Log("Video format restriction applied");
    }

    public void ExternalVideoSourceRawSample(uint durationMilliseconds, int width, int height, byte[] sample, VideoPixelFormatsEnum pixelFormat)
    {
        try
        {
            if (sample != null && sample.Length > 0)
            {
                // 处理外部视频源的原始样本
                Debug.Log($"Processing external video sample: {width}x{height}, {sample.Length} bytes, format: {pixelFormat}");

                // 触发原始视频样本事件
                OnVideoSourceRawSample?.Invoke(durationMilliseconds, width, height, sample, pixelFormat);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error processing external video sample: {ex.Message}");
            OnVideoSourceError?.Invoke($"External video sample error: {ex.Message}");
        }
    }

    public void ExternalVideoSourceRawSampleFaster(uint durationMilliseconds, SIPSorceryMedia.Abstractions.RawImage rawImage)
    {
        try
        {
            if (rawImage != null)
            {
                // 处理外部视频源的原始样本（更快版本）
                Debug.Log($"Processing external video sample (faster): {rawImage.Width}x{rawImage.Height}");

                // 触发原始视频样本事件（更快版本）
                OnVideoSourceRawSampleFaster?.Invoke(durationMilliseconds, rawImage);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error processing external video sample (faster): {ex.Message}");
            OnVideoSourceError?.Invoke($"External video sample (faster) error: {ex.Message}");
        }
    }

    public void ForceKeyFrame()
    {
        try
        {
            // 强制生成关键帧
            Debug.Log("Force key frame requested");
            // 这里可以设置标志，在下一次编码时生成关键帧
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error forcing key frame: {ex.Message}");
            OnVideoSourceError?.Invoke($"Force key frame error: {ex.Message}");
        }
    }

    public bool HasEncodedVideoSubscribers()
    {
        // 检查是否有编码视频订阅者
        return OnVideoSourceEncodedSample != null &&
               OnVideoSourceEncodedSample.GetInvocationList().Length > 0;
    }

    public bool IsVideoSourcePaused()
    {
        // 检查视频源是否暂停
        return !_isCapturing;
    }

    #endregion
    
    #region IVideoSink Implementation

    public event VideoSinkDecodedSampleDelegate OnVideoSinkDecodedSample;
    public event VideoSinkDecodedSampleFasterDelegate OnVideoSinkDecodedSampleFaster;

    public List<VideoFormat> GetVideoSinkFormats()
    {
        return GetVideoSourceFormats(); // 支持相同的格式
    }
    
    public void SetVideoSinkFormat(VideoFormat videoFormat)
    {
        Debug.Log($"Setting video sink format: {videoFormat.Codec} {videoFormat.Width}x{videoFormat.Height}");
        
        // 创建远程视频纹理
        _remoteVideoTexture = new Texture2D(videoFormat.Width, videoFormat.Height, TextureFormat.RGB24, false);
        if (remoteVideoDisplay != null)
        {
            remoteVideoDisplay.texture = _remoteVideoTexture;
        }
    }
    
    public void GotVideoRtp(IPEndPoint remoteEndPoint, uint ssrc, uint timestamp, 
                           uint payloadID, bool marker, byte[] payload)
    {
        try
        {
            if (payload != null && payload.Length > 0)
            {
                // 解码视频帧（这里需要集成FFmpeg解码器）
                var decodedFrame = DecodeVideoPayload(payload, (int)payloadID);
                if (decodedFrame != null)
                {
                    // 在主线程中更新纹理
                    UnityMainThreadDispatcher.Instance.Enqueue(() => UpdateRemoteVideoTexture(decodedFrame));
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error processing video RTP: {ex.Message}");
        }
    }

    public void GotVideoFrame(IPEndPoint remoteEndPoint, uint timestamp, byte[] frame, VideoFormat format)
    {
        try
        {
            if (frame != null && frame.Length > 0)
            {
                Debug.Log($"Received video frame: {frame.Length} bytes, format: {format.Codec}");

                // 在主线程中更新纹理
                UnityMainThreadDispatcher.Instance.Enqueue(() => UpdateRemoteVideoTexture(frame));

                // 触发解码视频样本事件
                OnVideoSinkDecodedSample?.Invoke(frame, format.Width, format.Height, format.Stride, format.FormatID);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error processing video frame: {ex.Message}");
        }
    }

    public Task PauseVideoSink()
    {
        try
        {
            Debug.Log("Video sink paused");
            // 暂停视频接收处理
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error pausing video sink: {ex.Message}");
            return Task.FromException(ex);
        }
    }

    public Task ResumeVideoSink()
    {
        try
        {
            Debug.Log("Video sink resumed");
            // 恢复视频接收处理
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error resuming video sink: {ex.Message}");
            return Task.FromException(ex);
        }
    }

    public Task StartVideoSink()
    {
        try
        {
            Debug.Log("Video sink started");
            // 启动视频接收处理
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error starting video sink: {ex.Message}");
            return Task.FromException(ex);
        }
    }

    public Task CloseVideoSink()
    {
        try
        {
            Debug.Log("Video sink closed");
            // 关闭视频接收处理
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error closing video sink: {ex.Message}");
            return Task.FromException(ex);
        }
    }

    #endregion
    
    #region Unity Video Management
    
    private void Awake()
    {
        InitializeVideoCapture();
        InitializeFFmpegCodecs();
    }

    private void InitializeVideoCapture()
    {
        try
        {
            if (WebCamTexture.devices.Length == 0)
            {
                Debug.LogWarning("No webcam devices found");
                return;
            }

            // 选择第一个可用的摄像头
            var deviceName = WebCamTexture.devices[0].name;
            _webCamTexture = new WebCamTexture(deviceName, videoWidth, videoHeight, frameRate);

            // 设置本地视频显示
            if (localVideoDisplay != null)
            {
                localVideoDisplay.texture = _webCamTexture;
            }

            _isInitialized = true;
            Debug.Log($"Video capture initialized: {deviceName}");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to initialize video capture: {ex.Message}");
        }
    }

    private void InitializeFFmpegCodecs()
    {
        try
        {
            // 暂时使用简单实现，后续集成FFmpeg
            // 创建FFmpeg视频编码器
            // _videoEncoder = new FFmpegVideoEncoder();

            // 创建FFmpeg视频解码器
            // _videoDecoder = new FFmpegVideoDecoder();

            Debug.Log("Video codecs initialized (using fallback implementation)");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to initialize video codecs: {ex.Message}");
            Debug.LogWarning("Video encoding/decoding will use fallback methods");
        }
    }
    
    private void StartWebCam()
    {
        if (!_isInitialized || _webCamTexture == null)
        {
            Debug.LogError("Video capture not initialized");
            return;
        }
        
        try
        {
            _webCamTexture.Play();
            
            // 启动视频捕获协程
            StartCoroutine(CaptureVideoCoroutine());
            
            Debug.Log("WebCam started");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to start webcam: {ex.Message}");
            throw;
        }
    }
    
    private void StopWebCam()
    {
        if (_webCamTexture != null && _webCamTexture.isPlaying)
        {
            _webCamTexture.Stop();
            Debug.Log("WebCam stopped");
        }
    }
    
    private System.Collections.IEnumerator CaptureVideoCoroutine()
    {
        var frameInterval = 1.0f / frameRate;
        
        while (_isCapturing && _webCamTexture.isPlaying)
        {
            if (_webCamTexture.didUpdateThisFrame)
            {
                // 捕获视频帧
                ProcessCapturedVideoFrame();
            }
            
            yield return new WaitForSeconds(frameInterval);
        }
    }
    
    private void ProcessCapturedVideoFrame()
    {
        try
        {
            if (_currentVideoFormat != null && _webCamTexture != null)
            {
                // 将WebCamTexture转换为字节数组
                var frameData = TextureToByteArray(_webCamTexture);
                
                if (frameData != null && frameData.Length > 0)
                {
                    // 编码视频帧（这里需要集成FFmpeg编码器）
                    var encodedSample = EncodeVideoFrame(frameData);
                    
                    if (encodedSample != null)
                    {
                        // 触发编码完成事件，SIPSorcery会自动发送RTP包
                        OnVideoSourceEncodedSample?.Invoke(encodedSample);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error encoding video frame: {ex.Message}");
        }
    }
    
    private byte[] TextureToByteArray(WebCamTexture texture)
    {
        try
        {
            // 创建临时RenderTexture
            var renderTexture = RenderTexture.GetTemporary(texture.width, texture.height);
            Graphics.Blit(texture, renderTexture);
            
            // 读取像素数据
            var tempTexture = new Texture2D(texture.width, texture.height, TextureFormat.RGB24, false);
            RenderTexture.active = renderTexture;
            tempTexture.ReadPixels(new Rect(0, 0, texture.width, texture.height), 0, 0);
            tempTexture.Apply();
            
            // 转换为字节数组
            var bytes = tempTexture.GetRawTextureData();
            
            // 清理资源
            RenderTexture.active = null;
            RenderTexture.ReleaseTemporary(renderTexture);
            DestroyImmediate(tempTexture);
            
            return bytes;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error converting texture to byte array: {ex.Message}");
            return null;
        }
    }
    
    private void UpdateRemoteVideoTexture(byte[] frameData)
    {
        try
        {
            if (_remoteVideoTexture != null && frameData != null)
            {
                _remoteVideoTexture.LoadRawTextureData(frameData);
                _remoteVideoTexture.Apply();
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error updating remote video texture: {ex.Message}");
        }
    }
    
    #endregion
    
    #region Video Encoding/Decoding (基于SIPSorceryMedia.FFmpeg)

    private EncodedSample EncodeVideoFrame(byte[] frameData)
    {
        try
        {
            if (_currentVideoFormat != null && frameData != null && frameData.Length > 0)
            {
                // 暂时使用简单编码（后续集成FFmpeg）
                // 实际项目中这里应该使用FFmpeg进行H.264或VP8编码
                return new EncodedSample
                {
                    Sample = frameData,
                    Timestamp = (uint)(DateTime.UtcNow.Ticks / TimeSpan.TicksPerMillisecond),
                    PayloadTypeID = (uint)_currentVideoFormat.FormatID
                };
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Video encoding failed: {ex.Message}");
        }

        // 后备方案：返回原始数据
        return new EncodedSample
        {
            Sample = frameData,
            Timestamp = (uint)(DateTime.UtcNow.Ticks / TimeSpan.TicksPerMillisecond),
            PayloadTypeID = _currentVideoFormat?.FormatID ?? 96
        };
    }

    private byte[] DecodeVideoPayload(byte[] payload, int payloadType)
    {
        try
        {
            if (_currentVideoFormat != null && payload != null && payload.Length > 0)
            {
                // 暂时返回原始数据（后续集成FFmpeg解码）
                // 实际项目中这里应该使用FFmpeg进行H.264或VP8解码
                return payload;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"FFmpeg video decoding failed: {ex.Message}");
        }

        // 后备方案：返回原始数据
        return payload;
    }

    #endregion
    
    #region Utility Methods
    
    public MediaEndPoints ToMediaEndPoints()
    {
        var endPoints = new MediaEndPoints();
        endPoints.VideoSource = this;
        endPoints.VideoSink = this;
        return endPoints;
    }
    
    private void OnDestroy()
    {
        StopWebCam();

        if (_remoteVideoTexture != null)
        {
            DestroyImmediate(_remoteVideoTexture);
        }

        // 清理FFmpeg编解码器
        try
        {
            // 清理编解码器资源（当集成FFmpeg时）
            Debug.Log("Video codecs cleaned up");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error disposing FFmpeg video codecs: {ex.Message}");
        }
    }
    
    #endregion
}
