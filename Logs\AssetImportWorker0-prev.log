Using pre-set license
Built from '2020.3/china_unity/release' branch; Version is '2020.3.48f1c1 (06fbdfbf16e3) revision 457695'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 16290 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
D:\Unity\2020.3.48f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/2024-5-15/UnityProject/SIPSorcerytest4.2
-logFile
Logs/AssetImportWorker0.log
-srvPort
1484
Successfully changed project path to: D:/2024-5-15/UnityProject/SIPSorcerytest4.2
D:/2024-5-15/UnityProject/SIPSorcerytest4.2
Using Asset Import Pipeline V2.
Player connection [20128] Host "[IP] ************* [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,xxzx-bzhzy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [20128] Host "[IP] ************* [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,xxzx-bzhzy) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.Refreshing native plugins compatible for Editor in 159.70 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1c1 (06fbdfbf16e3)
[Subsystems] Discovering subsystems at path D:/Unity/2020.3.48f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/2024-5-15/UnityProject/SIPSorcerytest4.2/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA GeForce GT 730 (ID=0x1287)
    Vendor:   
    VRAM:     2007 MB
    Driver:   30.0.14.7514
Initialize mono
Mono path[0] = 'D:/Unity/2020.3.48f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56956
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.006453 seconds.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 50.54 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.824 seconds
Domain Reload Profiling:
	ReloadAssembly (1824ms)
		BeginReloadAssembly (175ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1556ms)
			LoadAssemblies (174ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (369ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (602ms)
			SetupLoadedEditorAssemblies (312ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (21ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (51ms)
				BeforeProcessingInitializeOnLoad (38ms)
				ProcessInitializeOnLoadAttributes (155ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.024885 seconds.
Begin MonoManager ReloadAssembly
Error: Could not load signature of SIPSorceryMedia.FFmpeg.FFmpegAudioDecoder:.ctor due to: Could not load file or assembly 'FFmpeg.AutoGen, Version=*******, Culture=neutral, PublicKeyToken=null' or one of its dependencies. assembly:FFmpeg.AutoGen, Version=*******, Culture=neutral, PublicKeyToken=null type:<unknown type> member:(null) signature:<none>
Unloading broken assembly Assets/Plugins/SIPSorceryMedia.FFmpeg.dll, this assembly can cause crashes in the runtime
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 0.42 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.616 seconds
Domain Reload Profiling:
	ReloadAssembly (1617ms)
		BeginReloadAssembly (202ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (21ms)
		EndReloadAssembly (1317ms)
			LoadAssemblies (231ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (450ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (54ms)
			SetupLoadedEditorAssemblies (506ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (113ms)
				ProcessInitializeOnLoadAttributes (360ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.25 seconds
Refreshing native plugins compatible for Editor in 1.04 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1348 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 64.7 MB.
System memory in use after: 64.8 MB.

Unloading 30 unused Assets to reduce memory usage. Loaded Objects now: 1774.
Total: 15.904600 ms (FindLiveObjects: 0.173700 ms CreateObjectMapping: 0.076300 ms MarkObjects: 15.523800 ms  DeleteObjects: 0.129100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Plugins/SIPSorceryMedia.FFmpeg.dll
  artifactKey: Guid(34ce412c9062ddd4da80acc8fccbf1de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/SIPSorceryMedia.FFmpeg.dll using Guid(34ce412c9062ddd4da80acc8fccbf1de) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '06b6e27c7804a2dafc6beda61dcf8886') in 1.852108 seconds 
